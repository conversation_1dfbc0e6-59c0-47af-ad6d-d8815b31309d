#pragma once

#ifdef __APPLE__
	#define GL_SILENCE_DEPRECATION

#elif defined(_WIN32)

#else //Linux
	#define M_PI 3.14159265358979323846
#endif

// Application constants - Minimum 720p resolution
#define HUMANGL_DEFAULT_WINDOW_WIDTH 1280
#define HUMANGL_DEFAULT_WINDOW_HEIGHT 720
#define HUMANGL_WINDOW_TITLE "HumanGL - Skeletal Animation"

// OpenGL constants
#define HUMANGL_OPENGL_MAJOR_VERSION 2
#define HUMANGL_OPENGL_MINOR_VERSION 1
#define HUMANGL_DEPTH_BUFFER_SIZE 24

// Animation constants
#define HUMANGL_TARGET_FPS 60
#define HUMANGL_FRAME_DELAY 16  // ~60 FPS (1000ms / 60fps ≈ 16ms)

// Math utility macros
#define DEGREES_TO_RADIANS(degrees) ((degrees) * M_PI / 180.0f)
#define RADIANS_TO_DEGREES(radians) ((radians) * 180.0f / M_PI)

// Color constants (RGB values 0.0f - 1.0f)
#define HUMANGL_BACKGROUND_COLOR_R 0.2f
#define HUMANGL_BACKGROUND_COLOR_G 0.3f
#define HUMANGL_BACKGROUND_COLOR_B 0.4f
#define HUMANGL_BACKGROUND_COLOR_A 1.0f

// Default body part colors
#define HUMANGL_DEFAULT_SKIN_R 0.9f
#define HUMANGL_DEFAULT_SKIN_G 0.7f
#define HUMANGL_DEFAULT_SKIN_B 0.6f

#define HUMANGL_DEFAULT_CLOTHING_R 0.2f
#define HUMANGL_DEFAULT_CLOTHING_G 0.4f
#define HUMANGL_DEFAULT_CLOTHING_B 0.8f

// Camera and perspective constants
#define HUMANGL_DEFAULT_FOV 45.0f
#define HUMANGL_NEAR_PLANE 0.1f
#define HUMANGL_FAR_PLANE 100.0f
#define HUMANGL_CAMERA_DISTANCE -8.0f

// Animation timing constants
#define HUMANGL_WALKING_SPEED 0.08f
#define HUMANGL_JUMPING_SPEED 0.15f
#define HUMANGL_DANCING_SPEED 0.12f
#define HUMANGL_KUNGFU_SPEED 0.10f

// Menu UI constants
#define HUMANGL_MENU_BUTTON_WIDTH 200.0f
#define HUMANGL_MENU_BUTTON_HEIGHT 50.0f
#define HUMANGL_MENU_BUTTON_SPACING 60.0f
#define HUMANGL_MENU_TITLE_OFFSET_Y 120.0f
#define HUMANGL_MENU_SETTINGS_OFFSET_Y 150.0f
#define HUMANGL_MENU_INSTRUCTIONS_OFFSET_Y 100.0f

// Menu colors
#define HUMANGL_MENU_BUTTON_NORMAL_R 0.2f
#define HUMANGL_MENU_BUTTON_NORMAL_G 0.3f
#define HUMANGL_MENU_BUTTON_NORMAL_B 0.5f
#define HUMANGL_MENU_BUTTON_NORMAL_A 0.8f

#define HUMANGL_MENU_BUTTON_HOVER_R 0.3f
#define HUMANGL_MENU_BUTTON_HOVER_G 0.5f
#define HUMANGL_MENU_BUTTON_HOVER_B 0.8f
#define HUMANGL_MENU_BUTTON_HOVER_A 0.8f

#define HUMANGL_MENU_BUTTON_BORDER_R 0.8f
#define HUMANGL_MENU_BUTTON_BORDER_G 0.8f
#define HUMANGL_MENU_BUTTON_BORDER_B 0.8f

#define HUMANGL_MENU_TEXT_R 1.0f
#define HUMANGL_MENU_TEXT_G 1.0f
#define HUMANGL_MENU_TEXT_B 1.0f

// Text rendering constants
#define HUMANGL_TEXT_CHAR_WIDTH 10
#define HUMANGL_TEXT_HEIGHT 16
#define HUMANGL_TEXT_FONT_SIZE 16

// Credits menu layout constants
#define HUMANGL_CREDITS_CONTENT_START_Y 200.0f
#define HUMANGL_CREDITS_LINE_SPACING 30.0f
#define HUMANGL_CREDITS_LEFT_MARGIN 50.0f
#define HUMANGL_CREDITS_TITLE_OFFSET_X 150.0f
#define HUMANGL_CREDITS_INDENT 20.0f

// Animation cycle durations
#define HUMANGL_DANCING_CYCLE_DURATION 6.0f
#define HUMANGL_KUNGFU_CYCLE_DURATION 4.0f

// Walking animation constants
#define HUMANGL_WALK_LEG_SWING_AMPLITUDE 25.0f
#define HUMANGL_WALK_LOWER_LEG_BEND_AMPLITUDE 30.0f
#define HUMANGL_WALK_ARM_SWING_AMPLITUDE 30.0f
#define HUMANGL_WALK_FOREARM_BEND_AMPLITUDE 40.0f
#define HUMANGL_WALK_ARM_SWAY_AMPLITUDE 10.0f
#define HUMANGL_WALK_FOREARM_PHASE_OFFSET 0.3f
#define HUMANGL_WALK_TORSO_SWAY_AMPLITUDE 2.0f
#define HUMANGL_WALK_HEAD_BOB_AMPLITUDE 1.5f
#define HUMANGL_WALK_VERTICAL_BOB_AMPLITUDE 0.1f
#define HUMANGL_WALK_ANIMATION_FREQUENCY 2.0f

// Kung Fu animation constants
#define HUMANGL_KUNGFU_GUARD_STANCE_DURATION 0.5f
#define HUMANGL_KUNGFU_PUNCH_DURATION 0.5f
#define HUMANGL_KUNGFU_RECOVERY_DURATION 0.5f
#define HUMANGL_KUNGFU_KICK_PREP_DURATION 0.5f
#define HUMANGL_KUNGFU_KICK_DURATION 0.5f
#define HUMANGL_KUNGFU_BLOCK_DURATION 0.5f

#define HUMANGL_KUNGFU_ARM_GUARD_ANGLE 45.0f
#define HUMANGL_KUNGFU_ARM_GUARD_SPREAD 25.0f
#define HUMANGL_KUNGFU_ARM_GUARD_FOREARM 15.0f
#define HUMANGL_KUNGFU_PUNCH_EXTENSION 80.0f
#define HUMANGL_KUNGFU_LEG_STANCE_ANGLE 10.0f
#define HUMANGL_KUNGFU_TORSO_LEAN_ANGLE 5.0f
#define HUMANGL_KUNGFU_KICK_HEIGHT 120.0f
#define HUMANGL_KUNGFU_HEAD_FOCUS_X 2.5f
#define HUMANGL_KUNGFU_HEAD_FOCUS_Y 3.0f
#define HUMANGL_KUNGFU_BOUNCE_AMPLITUDE 0.05f

// Dancing animation constants
#define HUMANGL_DANCE_CROUCH_LEG_ANGLE 25.0f
#define HUMANGL_DANCE_CROUCH_LEG_BEND 50.0f
#define HUMANGL_DANCE_ARM_PLANT_ANGLE 90.0f
#define HUMANGL_DANCE_ARM_PLANT_FOREARM 60.0f
#define HUMANGL_DANCE_TORSO_CROUCH_ANGLE 10.0f
#define HUMANGL_DANCE_SPIN_ANGLE 360.0f
#define HUMANGL_DANCE_LEG_KICK_AMPLITUDE 60.0f
#define HUMANGL_DANCE_LEG_KICK_BASE 40.0f
#define HUMANGL_DANCE_FREEZE_ARM_ANGLE_L 80.0f
#define HUMANGL_DANCE_FREEZE_ARM_ANGLE_R 100.0f
#define HUMANGL_DANCE_FREEZE_ARM_SPREAD 20.0f
#define HUMANGL_DANCE_FREEZE_ARM_FOREARM 40.0f
#define HUMANGL_DANCE_FREEZE_LEG_ANGLE 20.0f
#define HUMANGL_DANCE_FREEZE_LEG_BEND 10.0f
#define HUMANGL_DANCE_SWEEP_AMPLITUDE 45.0f
#define HUMANGL_DANCE_HEAD_X_AMPLITUDE 5.0f
#define HUMANGL_DANCE_HEAD_Y_AMPLITUDE 3.0f
#define HUMANGL_DANCE_BOUNCE_AMPLITUDE 0.05f

// Body part positioning constants
#define HUMANGL_HEAD_Y_POSITION 1.4f
#define HUMANGL_HEAD_SCALE 0.6f
#define HUMANGL_NECK_Y_POSITION 0.9f
#define HUMANGL_NECK_SCALE 0.3f
#define HUMANGL_TORSO_SCALE_X 1.0f
#define HUMANGL_TORSO_SCALE_Y 1.5f
#define HUMANGL_TORSO_SCALE_Z 0.5f
#define HUMANGL_SHOULDER_SCALE 0.3f

// Arm positioning constants
#define HUMANGL_LEFT_ARM_X_POSITION -0.7f
#define HUMANGL_RIGHT_ARM_X_POSITION 0.7f
#define HUMANGL_ARM_Y_POSITION 0.5f
#define HUMANGL_ARM_Z_POSITION 0.0f

// Eye positioning constants
#define HUMANGL_EYE_Y_OFFSET 1.5f
#define HUMANGL_EYE_SCALE 0.1f
#define HUMANGL_LEFT_EYE_X_OFFSET -0.15f
#define HUMANGL_RIGHT_EYE_X_OFFSET 0.15f
#define HUMANGL_EYE_Z_OFFSET 0.25f

// Leg positioning and scaling constants
#define HUMANGL_LEFT_LEG_X_POSITION -0.3f
#define HUMANGL_RIGHT_LEG_X_POSITION 0.3f
#define HUMANGL_LEG_Y_POSITION -0.75f
#define HUMANGL_LEG_Z_POSITION 0.0f
#define HUMANGL_THIGH_Y_OFFSET -0.4f
#define HUMANGL_THIGH_SCALE_X 0.3f
#define HUMANGL_THIGH_SCALE_Y 0.8f
#define HUMANGL_THIGH_SCALE_Z 0.3f
#define HUMANGL_LOWER_LEG_Y_OFFSET -0.8f
#define HUMANGL_LOWER_LEG_Y_POSITION -0.4f
#define HUMANGL_LOWER_LEG_SCALE_X 0.25f
#define HUMANGL_LOWER_LEG_SCALE_Y 0.8f
#define HUMANGL_LOWER_LEG_SCALE_Z 0.25f

// Arm positioning and scaling constants
#define HUMANGL_UPPER_ARM_Y_OFFSET -0.4f
#define HUMANGL_UPPER_ARM_SCALE_X 0.3f
#define HUMANGL_UPPER_ARM_SCALE_Y 0.8f
#define HUMANGL_UPPER_ARM_SCALE_Z 0.3f
#define HUMANGL_FOREARM_Y_OFFSET -0.8f
#define HUMANGL_FOREARM_Y_POSITION -0.4f
#define HUMANGL_FOREARM_SCALE_X 0.25f
#define HUMANGL_FOREARM_SCALE_Y 0.8f
#define HUMANGL_FOREARM_SCALE_Z 0.25f

// Body part colors - Pants/Clothing
#define HUMANGL_PANTS_COLOR_R 0.1f
#define HUMANGL_PANTS_COLOR_G 0.2f
#define HUMANGL_PANTS_COLOR_B 0.6f

// Cube vertex constants (for drawColoredCube)
#define HUMANGL_CUBE_HALF_SIZE 0.5f
#define HUMANGL_CUBE_NEGATIVE_HALF -0.5f

// Camera control constants
#define HUMANGL_CAMERA_DEFAULT_DISTANCE 8.0f
#define HUMANGL_CAMERA_DEFAULT_HEIGHT 0.0f
#define HUMANGL_CAMERA_DEFAULT_ROTATION_Y 0.0f
#define HUMANGL_TORSO_ROTATION_SPEED 2.0f
#define HUMANGL_HEAD_ROTATION_SPEED 1.0f
#define HUMANGL_ARM_ROTATION_SPEED 2.0f
#define HUMANGL_LEG_ROTATION_SPEED 2.0f
#define HUMANGL_CAMERA_ROTATION_SPEED 2.0f
#define HUMANGL_CAMERA_ZOOM_SPEED 0.5f
#define HUMANGL_CAMERA_HEIGHT_SPEED 0.2f

// Simulation renderer constants
#define HUMANGL_SIMULATION_NEAR_PLANE 1.0f
#define HUMANGL_SIMULATION_FAR_PLANE 100.0f
#define HUMANGL_SIMULATION_FOV 45.0f

// OpenGL rendering constants
#define HUMANGL_OPENGL_AXIS_X 1.0f
#define HUMANGL_OPENGL_AXIS_Y 1.0f
#define HUMANGL_OPENGL_AXIS_Z 1.0f
#define HUMANGL_OPENGL_AXIS_NONE 0.0f

// Animation timing multipliers
#define HUMANGL_DANCE_RHYTHM_FREQUENCY 6.0f
#define HUMANGL_DANCE_HEAD_X_FREQUENCY 3.0f
#define HUMANGL_DANCE_HEAD_Y_FREQUENCY 2.0f
#define HUMANGL_KUNGFU_HEAD_X_FREQUENCY 1.2f
#define HUMANGL_KUNGFU_HEAD_Y_FREQUENCY 1.0f
#define HUMANGL_KUNGFU_BOUNCE_FREQUENCY 4.0f

// Settings menu text positioning
#define HUMANGL_SETTINGS_CONTENT_START_Y 200.0f
#define HUMANGL_SETTINGS_LINE_SPACING 30.0f
#define HUMANGL_SETTINGS_LEFT_MARGIN 50.0f
#define HUMANGL_SETTINGS_INDENT 20.0f

// Instructions menu positioning
#define HUMANGL_INSTRUCTIONS_CONTENT_START_Y 150.0f
#define HUMANGL_INSTRUCTIONS_LINE_SPACING 40.0f
#define HUMANGL_INSTRUCTIONS_LEFT_MARGIN 100.0f

// Title positioning constants
#define HUMANGL_TITLE_Y_POSITION 100.0f
#define HUMANGL_TITLE_X_OFFSET 50.0f

// Additional color constants for simulation background
#define HUMANGL_SIMULATION_BACKGROUND_R 0.1f
#define HUMANGL_SIMULATION_BACKGROUND_G 0.1f
#define HUMANGL_SIMULATION_BACKGROUND_B 0.3f
#define HUMANGL_SIMULATION_BACKGROUND_A 1.0f

// Matrix constants
#define HUMANGL_MATRIX_SIZE 16
#define HUMANGL_MATRIX_DIMENSION 4
#define HUMANGL_MATRIX_IDENTITY_STEP 5

// Perspective calculation constants
#define HUMANGL_PERSPECTIVE_DIVISOR 2.0f
#define HUMANGL_PERSPECTIVE_MULTIPLIER 360.0f

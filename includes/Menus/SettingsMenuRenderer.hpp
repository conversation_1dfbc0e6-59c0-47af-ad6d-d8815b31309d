#pragma once

#include "../humangl.hpp"
#include "MenuRenderer.hpp"
#include "TextRenderer.hpp"
#include <vector>
#include <map>
#include <string>

class SettingsMenuRenderer : public MenuRenderer {
private:
    std::vector<MenuButton> buttons;
    SettingsPage currentPage;
    BodyPart selectedBodyPart;
    std::map<BodyPart, BodyPartSettings> bodyPartSettings;
    int currentColorIndex;
    std::vector<Color> availableColors;

public:
    SettingsMenuRenderer(TextRenderer& textRenderer);
    virtual ~SettingsMenuRenderer() = default;

    // Initialize menu buttons
    void initializeButtons();

    // Override the base class render method
    void render(const std::vector<MenuButton>& buttons) override;

    // Render this menu without external buttons
    void render();

    // Get buttons for interaction
    const std::vector<MenuButton>& getButtons() const;

    // Handle button clicks
    MenuAction handleButtonClick(int buttonIndex);

    // Page navigation
    void setPage(SettingsPage page);
    void setSelectedBodyPart(BodyPart part);
    SettingsPage getCurrentPage() const;

    // Body part customization
    void cycleBodyPartColor();
    void adjustBodyPartScale(float scaleMultiplier);
    const BodyPartSettings& getBodyPartSettings(BodyPart part) const;

private:
    // Settings menu specific rendering methods
    void renderTitle();
    void renderSettingsContent();
    void renderBodyPartSelection();
    void renderBodyPartCustomization();

    // UI helper methods
    void initializeAvailableColors();
    void drawSlider(float x, float y, float value, const std::string& label);
    void drawColorSelector(float x, float y);
    std::string getBodyPartName(BodyPart part) const;
};

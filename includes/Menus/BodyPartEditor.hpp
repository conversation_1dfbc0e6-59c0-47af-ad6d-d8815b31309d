#pragma once

#include "../humangl.hpp"
#include "MenuRenderer.hpp"
#include "TextRenderer.hpp"

class BodyPartEditor : public MenuRenderer {
private:
    TextRenderer& textRenderer;
    BodyPart selectedBodyPart;
    BodyPartSettings bodyPartSettings;
    std::vector<Color> availableColors;
    int currentColorIndex;
    
    // UI element positions for mouse interaction
    float sliderX, sliderY, sliderWidth, sliderHeight;
    float colorSelectorX, colorSelectorY, colorSelectorSize;
    bool isDraggingSlider;
    
    // Input handling state
    const Uint8* keyboardState;
    bool plusKeyPressed;
    bool minusKeyPressed;
    bool cKeyPressed;
    
public:
    BodyPartEditor(TextRenderer& textRenderer);
    virtual ~BodyPartEditor() = default;
    
    // Set the body part to edit
    void setBodyPart(BodyPart part);
    void setBodyPartSettings(const BodyPartSettings& settings);
    
    // Initialize buttons for this editor
    void initializeButtons();
    
    // Render the body part editor
    void render(const std::vector<MenuButton>& buttons) override;
    void render();
    
    // Get buttons for interaction
    const std::vector<MenuButton>& getButtons() const;
    
    // Handle button clicks
    MenuAction handleButtonClick(int buttonIndex);
    
    // Mouse interaction
    bool handleMouseClick(float mouseX, float mouseY);
    void updateHover(float mouseX, float mouseY);
    void handleMouseUp();
    
    // Input handling
    void handleInput();
    
    // Body part customization
    void cycleBodyPartColor();
    void adjustBodyPartScale(float scaleMultiplier);
    void setBodyPartScale(float newScale);
    void resetBodyPartToDefault();
    
    // Get current settings
    const BodyPartSettings& getBodyPartSettings() const { return bodyPartSettings; }
    BodyPart getSelectedBodyPart() const { return selectedBodyPart; }
    
private:
    // Helper methods
    void initializeAvailableColors();
    void drawSlider(float x, float y, float value, const std::string& label);
    void drawColorSelector(float x, float y);
    std::string getBodyPartName(BodyPart part) const;
    BodyPartSettings getDefaultBodyPartSettings(BodyPart part) const;
    void renderTitle();
    void renderCustomizationContent();
    void handleCustomizationInput();
};

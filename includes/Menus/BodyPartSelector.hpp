#pragma once

#include "../humangl.hpp"
#include "MenuRenderer.hpp"
#include "TextRenderer.hpp"

class BodyPartSelector : public MenuRenderer {
private:
    TextRenderer& textRenderer;
    std::vector<BodyPart> customizableBodyParts;
    
public:
    BodyPartSelector(TextRenderer& textRenderer);
    virtual ~BodyPartSelector() = default;
    
    // Initialize body part selection buttons
    void initializeButtons();
    
    // Render the body part selection grid
    void render(const std::vector<MenuButton>& buttons) override;
    void render();
    
    // Get buttons for interaction
    const std::vector<MenuButton>& getButtons() const;
    
    // Handle button clicks - returns selected body part
    BodyPart handleButtonClick(int buttonIndex);
    
    // Get list of customizable body parts
    std::vector<BodyPart> getCustomizableBodyParts() const;
    
private:
    // Helper methods
    std::string getBodyPartName(BodyPart part) const;
    void renderTitle();
    void renderInstructions();
};

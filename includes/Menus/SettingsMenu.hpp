#pragma once

#include "../humangl.hpp"
#include "MenuRenderer.hpp"
#include "TextRenderer.hpp"

class SettingsMenu : public MenuRenderer {
private:
    TextRenderer& textRenderer;
    
public:
    SettingsMenu(TextRenderer& textRenderer);
    virtual ~SettingsMenu() = default;
    
    // Initialize main settings buttons
    void initializeButtons();
    
    // Render the main settings menu
    void render(const std::vector<MenuButton>& buttons) override;
    void render();
    
    // Get buttons for interaction
    const std::vector<MenuButton>& getButtons() const;
    
    // Handle button clicks
    MenuAction handleButtonClick(int buttonIndex);
    
private:
    // Helper methods
    void renderTitle();
    void renderSettingsContent();
};

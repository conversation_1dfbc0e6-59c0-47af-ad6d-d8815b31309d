#pragma once

#include "../humangl.hpp"
#include "MenuRenderer.hpp"
#include "TextRenderer.hpp"

class BackgroundCustomizer : public MenuRenderer {
private:
    TextRenderer& textRenderer;
    std::vector<Color> availableColors;
    
    // Background colors
    Color menuBackgroundColor;
    Color simulationBackgroundColor;
    int menuBgColorIndex;
    int simulationBgColorIndex;
    
    // UI element positions for mouse interaction
    float menuColorSelectorX, menuColorSelectorY, menuColorSelectorSize;
    float simColorSelectorX, simColorSelectorY, simColorSelectorSize;
    
    // Input handling state
    const Uint8* keyboardState;
    bool plusKeyPressed;  // Reused for M key
    bool minusKeyPressed; // Reused for S key
    
public:
    BackgroundCustomizer(TextRenderer& textRenderer);
    virtual ~BackgroundCustomizer() = default;
    
    // Initialize buttons
    void initializeButtons();
    
    // Render the background customizer
    void render(const std::vector<MenuButton>& buttons) override;
    void render();
    
    // Get buttons for interaction
    const std::vector<MenuButton>& getButtons() const;
    
    // Handle button clicks
    MenuAction handleButtonClick(int buttonIndex);
    
    // Mouse interaction
    bool handleMouseClick(float mouseX, float mouseY);
    
    // Input handling
    void handleInput();
    
    // Background color customization
    void cycleMenuBackgroundColor();
    void cycleSimulationBackgroundColor();
    void resetColorsToDefault();
    
    // Get current colors
    const Color& getMenuBackgroundColor() const { return menuBackgroundColor; }
    const Color& getSimulationBackgroundColor() const { return simulationBackgroundColor; }
    
private:
    // Helper methods
    void initializeAvailableColors();
    void drawBackgroundColorSelector(float x, float y, const Color& currentColor, bool isMenuColor);
    void renderTitle();
    void renderCustomizationContent();
    void handleBackgroundCustomizationInput();
};

#pragma once

// ============================================================================
// CORE CSTYLE HEADERS (Order matters!)
// ============================================================================

// 1. Defines and constants (must come first)
#include "CStyle/defines.hpp"

// 2. Library includes (SDL2, OpenGL, STL)
#include "CStyle/libs.hpp"

// 3. Enumerations (depend on basic types)
#include "CStyle/enums.hpp"

// 4. Structures (depend on enums and libs)
#include "CStyle/structs.hpp"

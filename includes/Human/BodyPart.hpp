#pragma once

#include "../humangl.hpp"

// Base class for all body part renderers
class BodyPartRenderer {
protected:
    float colorR, colorG, colorB;
    float scaleX, scaleY, scaleZ;

public:
    BodyPartRenderer(float r = 1.0f, float g = 1.0f, float b = 1.0f);
    virtual ~BodyPartRenderer() = default;

    // Pure virtual render method
    virtual void render() = 0;

    // Color management
    void setColor(float r, float g, float b);

    // Scale management
    void setScale(float scaleX, float scaleY, float scaleZ);

protected:
    // Helper method for drawing a colored cube
    void drawColoredCube(float r, float g, float b);
};

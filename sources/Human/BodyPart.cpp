#include "../../includes/Human/BodyPart.hpp"

BodyPartRenderer::BodyPartRenderer(float r, float g, float b)
    : colorR(r), colorG(g), colorB(b), scaleX(1.0f), scaleY(1.0f), scaleZ(1.0f) {
}

void BodyPartRenderer::setColor(float r, float g, float b) {
    colorR = r;
    colorG = g;
    colorB = b;
}

void BodyPartRenderer::setScale(float sX, float sY, float sZ) {
    scaleX = sX;
    scaleY = sY;
    scaleZ = sZ;
}

void BodyPartRenderer::drawColoredCube(float r, float g, float b) {
    // Ensure color is set properly
    glColor3f(r, g, b);

    glBegin(GL_QUADS);

    // Front face
    glVertex3f(HUMANGL_CUBE_NEGATIVE_HALF, HUMANGL_CUBE_NEGATIVE_HALF,  HUMANGL_CUBE_HALF_SIZE);
    glVertex3f( HUMANGL_CUBE_HALF_SIZE, HUMANGL_CUBE_NEGATIVE_HALF,  HUMANGL_CUBE_HALF_SIZE);
    glVertex3f( HUMANGL_CUBE_HALF_SIZE,  HUMANGL_CUBE_HALF_SIZE,  HUMANGL_CUBE_HALF_SIZE);
    glVertex3f(HUMANGL_CUBE_NEGATIVE_HALF,  HUMANGL_CUBE_HALF_SIZE,  HUMANGL_CUBE_HALF_SIZE);

    // Back face
    glVertex3f(HUMANGL_CUBE_NEGATIVE_HALF, HUMANGL_CUBE_NEGATIVE_HALF, HUMANGL_CUBE_NEGATIVE_HALF);
    glVertex3f(HUMANGL_CUBE_NEGATIVE_HALF,  HUMANGL_CUBE_HALF_SIZE, HUMANGL_CUBE_NEGATIVE_HALF);
    glVertex3f( HUMANGL_CUBE_HALF_SIZE,  HUMANGL_CUBE_HALF_SIZE, HUMANGL_CUBE_NEGATIVE_HALF);
    glVertex3f( HUMANGL_CUBE_HALF_SIZE, HUMANGL_CUBE_NEGATIVE_HALF, HUMANGL_CUBE_NEGATIVE_HALF);

    // Top face
    glVertex3f(HUMANGL_CUBE_NEGATIVE_HALF,  HUMANGL_CUBE_HALF_SIZE, HUMANGL_CUBE_NEGATIVE_HALF);
    glVertex3f(HUMANGL_CUBE_NEGATIVE_HALF,  HUMANGL_CUBE_HALF_SIZE,  HUMANGL_CUBE_HALF_SIZE);
    glVertex3f( HUMANGL_CUBE_HALF_SIZE,  HUMANGL_CUBE_HALF_SIZE,  HUMANGL_CUBE_HALF_SIZE);
    glVertex3f( HUMANGL_CUBE_HALF_SIZE,  HUMANGL_CUBE_HALF_SIZE, HUMANGL_CUBE_NEGATIVE_HALF);

    // Bottom face
    glVertex3f(HUMANGL_CUBE_NEGATIVE_HALF, HUMANGL_CUBE_NEGATIVE_HALF, HUMANGL_CUBE_NEGATIVE_HALF);
    glVertex3f( HUMANGL_CUBE_HALF_SIZE, HUMANGL_CUBE_NEGATIVE_HALF, HUMANGL_CUBE_NEGATIVE_HALF);
    glVertex3f( HUMANGL_CUBE_HALF_SIZE, HUMANGL_CUBE_NEGATIVE_HALF,  HUMANGL_CUBE_HALF_SIZE);
    glVertex3f(HUMANGL_CUBE_NEGATIVE_HALF, HUMANGL_CUBE_NEGATIVE_HALF,  HUMANGL_CUBE_HALF_SIZE);

    // Right face
    glVertex3f( HUMANGL_CUBE_HALF_SIZE, HUMANGL_CUBE_NEGATIVE_HALF, HUMANGL_CUBE_NEGATIVE_HALF);
    glVertex3f( HUMANGL_CUBE_HALF_SIZE,  HUMANGL_CUBE_HALF_SIZE, HUMANGL_CUBE_NEGATIVE_HALF);
    glVertex3f( HUMANGL_CUBE_HALF_SIZE,  HUMANGL_CUBE_HALF_SIZE,  HUMANGL_CUBE_HALF_SIZE);
    glVertex3f( HUMANGL_CUBE_HALF_SIZE, HUMANGL_CUBE_NEGATIVE_HALF,  HUMANGL_CUBE_HALF_SIZE);

    // Left face
    glVertex3f(HUMANGL_CUBE_NEGATIVE_HALF, HUMANGL_CUBE_NEGATIVE_HALF, HUMANGL_CUBE_NEGATIVE_HALF);
    glVertex3f(HUMANGL_CUBE_NEGATIVE_HALF, HUMANGL_CUBE_NEGATIVE_HALF,  HUMANGL_CUBE_HALF_SIZE);
    glVertex3f(HUMANGL_CUBE_NEGATIVE_HALF,  HUMANGL_CUBE_HALF_SIZE,  HUMANGL_CUBE_HALF_SIZE);
    glVertex3f(HUMANGL_CUBE_NEGATIVE_HALF,  HUMANGL_CUBE_HALF_SIZE, HUMANGL_CUBE_NEGATIVE_HALF);

    glEnd();
}

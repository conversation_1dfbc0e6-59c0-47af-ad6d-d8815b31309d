#include "../../includes/Human/Neck.hpp"

Neck::Neck() : BodyPart<PERSON>enderer(HUMANGL_DEFAULT_SKIN_R, HUMANGL_DEFAULT_SKIN_G, HUMANGL_DEFAULT_SKIN_B) {
    // Skin color
}

void Neck::render() {
    glPushMatrix();
    glTranslatef(HUMANGL_OPENGL_AXIS_NONE, HUMANGL_NECK_Y_POSITION, HUMANGL_OPENGL_AXIS_NONE);
    glScalef(HUMANGL_NECK_SCALE * scaleX, HUMANGL_NECK_SCALE * scaleY, HUMANGL_NECK_SCALE * scaleZ);
    drawColoredCube(colorR, colorG, colorB);
    glPopMatrix();
}

void Neck::render(MatrixStack& matrixStack) {
    // Use custom matrix stack for transformations (PDF compliant)
    matrixStack.pushMatrix();
    matrixStack.translate(HUMANGL_OPENGL_AXIS_NONE, HUMANGL_NECK_Y_POSITION, HUMANGL_OPENGL_AXIS_NONE);
    matrixStack.scale(HUMANGL_NECK_SCALE * scaleX, HUMANGL_NECK_SCALE * scaleY, HUMANGL_NECK_SCALE * scaleZ);
    matrixStack.applyToOpenGL();
    drawColoredCube(colorR, colorG, colorB);
    matrixStack.popMatrix();
}

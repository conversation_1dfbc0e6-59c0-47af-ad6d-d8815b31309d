#include "../../includes/Human/Leg.hpp"

Leg::Leg(float x, float y, float z)
    : BodyPart<PERSON>enderer(HUMANGL_DEFAULT_SKIN_R, HUMANGL_DEFAULT_SKIN_G, HUMANGL_DEFAULT_SKIN_B), positionX(x), positionY(y), positionZ(z),
      thighX(HUMANGL_OPENGL_AXIS_NONE), lowerLegX(HUMANGL_OPENGL_AXIS_NONE) {
    // Skin color for legs (will be overridden for thighs with pants color)
}

void Leg::render() {
    // Draw the entire leg as a connected hierarchy
    glPushMatrix();
    glTranslatef(positionX, positionY, positionZ);
    glRotatef(thighX, HUMANGL_OPENGL_AXIS_X, HUMANGL_OPENGL_AXIS_NONE, HUMANGL_OPENGL_AXIS_NONE);

    // Draw thigh (with pants color)
    glPushMatrix();
    glTranslatef(HUMANGL_OPENGL_AXIS_NONE, HUMANGL_THIGH_Y_OFFSET, HUMANGL_OPENGL_AXIS_NONE);
    glScalef(HUMANGL_THIGH_SCALE_X, HUMANGL_THIGH_SCALE_Y, HUMANGL_THIGH_SCALE_Z);
    drawColoredCube(HUMANGL_PANTS_COLOR_R, HUMANGL_PANTS_COLOR_G, HUMANGL_PANTS_COLOR_B);  // Dark blue pants color for thighs
    glPopMatrix();

    // Draw lower leg (connected to thigh)
    glTranslatef(HUMANGL_OPENGL_AXIS_NONE, HUMANGL_LOWER_LEG_Y_OFFSET, HUMANGL_OPENGL_AXIS_NONE);
    glRotatef(lowerLegX, HUMANGL_OPENGL_AXIS_X, HUMANGL_OPENGL_AXIS_NONE, HUMANGL_OPENGL_AXIS_NONE);
    glTranslatef(HUMANGL_OPENGL_AXIS_NONE, HUMANGL_LOWER_LEG_Y_POSITION, HUMANGL_OPENGL_AXIS_NONE);
    glScalef(HUMANGL_LOWER_LEG_SCALE_X, HUMANGL_LOWER_LEG_SCALE_Y, HUMANGL_LOWER_LEG_SCALE_Z);
    drawColoredCube(colorR, colorG, colorB);  // Skin color for lower legs

    glPopMatrix();
}

void Leg::setThighRotation(float x) {
    thighX = x;
}

void Leg::setLowerLegRotation(float x) {
    lowerLegX = x;
}

void Leg::getThighRotation(float& x) const {
    x = thighX;
}

void Leg::getLowerLegRotation(float& x) const {
    x = lowerLegX;
}

LeftLeg::LeftLeg() : Leg(HUMANGL_LEFT_LEG_X_POSITION, HUMANGL_LEG_Y_POSITION, HUMANGL_LEG_Z_POSITION) {
}

RightLeg::RightLeg() : Leg(HUMANGL_RIGHT_LEG_X_POSITION, HUMANGL_LEG_Y_POSITION, HUMANGL_LEG_Z_POSITION) {
}

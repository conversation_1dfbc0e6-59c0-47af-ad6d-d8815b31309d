#include "../../includes/Application/SettingsMenu.hpp"

SettingsMenu::SettingsMenu(SettingsMenuRenderer& renderer, MouseHandler& mouseHandler, MenuInputInterface& menuInput, int winWidth, int winHeight)
    : BaseMenu(mouseHandler, menuInput, winWidth, winHeight), renderer(renderer) {
    initializeButtons();
}

void SettingsMenu::initializeButtons() {
    // Get buttons from the renderer (which manages the current page's buttons)
    const std::vector<MenuButton>& rendererButtons = renderer.getButtons();
    buttons = rendererButtons;
}

void SettingsMenu::render() {
    renderer.render();
}

MenuAction SettingsMenu::handleButtonClick(int buttonIndex) {
    // Delegate to the renderer which handles the complex navigation logic
    MenuAction action = renderer.handleButtonClick(buttonIndex);
    
    // After handling the button click, update our buttons to match the new state
    initializeButtons();
    
    return action;
}

MenuAction SettingsMenu::handleEvent(const SDL_Event& event) {
    MenuAction action = MENU_ACTION_NONE;
    
    switch (event.type) {
        case SDL_MOUSEMOTION:
            mouseHandler.updateMousePosition(event.motion.x, event.motion.y);
            updateButtonHover();
            // Also update hover for custom UI elements (sliders, color selectors)
            renderer.updateHover(static_cast<float>(event.motion.x), static_cast<float>(event.motion.y));
            break;
            
        case SDL_MOUSEBUTTONDOWN:
            if (event.button.button == SDL_BUTTON_LEFT) {
                mouseHandler.setMousePressed(true);
            }
            break;
            
        case SDL_MOUSEBUTTONUP:
            if (event.button.button == SDL_BUTTON_LEFT) {
                mouseHandler.setMouseReleased(true);
                
                // First check if it's a button click
                int clickedButton = checkButtonClick();
                if (clickedButton >= 0) {
                    action = handleButtonClick(clickedButton);
                } else {
                    // If no button was clicked, check for custom UI interactions (sliders, color selectors)
                    float mouseX = static_cast<float>(event.button.x);
                    float mouseY = static_cast<float>(event.button.y);
                    renderer.handleMouseClick(mouseX, mouseY);
                }
                
                // Handle mouse up for custom UI elements
                renderer.handleMouseUp();
            }
            break;
            
        case SDL_KEYDOWN:
            menuInput.handleKeyEvent(event);
            break;
            
        default:
            break;
    }
    
    return action;
}

MenuAction SettingsMenu::update() {
    MenuAction action = MENU_ACTION_NONE;

    // Update input system
    menuInput.update();
    updateInput();

    // Handle settings-specific input (keyboard shortcuts for customization)
    renderer.handleInput();

    // Check for menu navigation keys
    if (isEscapePressed()) {
        action = MENU_ACTION_BACK_TO_MENU;
    }

    // Update buttons in case the page changed
    const std::vector<MenuButton>& rendererButtons = renderer.getButtons();
    if (buttons.size() != rendererButtons.size()) {
        initializeButtons();
    }

    // Reset input states
    menuInput.resetKeyStates();
    mouseHandler.resetMouseState();

    return action;
}

void SettingsMenu::updateButtonHover() {
    // Update hover for the current page's buttons
    mouseHandler.updateButtonHover(buttons);
    // Also update hover for the renderer's button system
    renderer.updateButtonHover(mouseHandler);
}

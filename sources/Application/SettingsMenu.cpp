#include "../../includes/Application/SettingsMenu.hpp"



SettingsMenu::SettingsMenu(SettingsMenuRenderer& renderer, MouseHandler& mouseHandler, MenuInput& menuInput, int winWidth, int winHeight)
    : BaseMenu(mouseHandler, menuInput, winWidth, winHeight), renderer(renderer) {
    initializeButtons();
}

void SettingsMenu::initializeButtons() {
    // Delegate button initialization to the renderer
    renderer.initializeButtons();

    // Get the buttons from the renderer
    buttons = renderer.getButtons();
}

void SettingsMenu::render() {
    // Let the renderer handle its own rendering
    renderer.render();
}

MenuAction SettingsMenu::handleButtonClick(int buttonIndex) {
    // Delegate button handling to the renderer
    MenuAction action = renderer.handleButtonClick(buttonIndex);

    // Update our buttons after any page changes
    buttons = renderer.getButtons();

    return action;
}

MenuAction SettingsMenu::handleEvent(const SDL_Event& event) {
    MenuAction action = MENU_ACTION_NONE;

    switch (event.type) {
        case SDL_MOUSEMOTION: {
            // Update button hover state
            updateButtonHover();

            // Update custom UI element hover state
            float mouseX = static_cast<float>(event.motion.x);
            float mouseY = static_cast<float>(event.motion.y);
            renderer.updateHover(mouseX, mouseY);
            break;
        }

        case SDL_MOUSEBUTTONDOWN:
            if (event.button.button == SDL_BUTTON_LEFT) {
                mouseHandler.setMousePressed(true);

                // Check for clicks on custom UI elements
                float mouseX = static_cast<float>(event.button.x);
                float mouseY = static_cast<float>(event.button.y);
                if (renderer.handleMouseClick(mouseX, mouseY)) {
                    // Click was handled by custom UI
                    return MENU_ACTION_NONE;
                }
            }
            break;

        case SDL_MOUSEBUTTONUP:
            if (event.button.button == SDL_BUTTON_LEFT) {
                mouseHandler.setMouseReleased(true);
                int clickedButton = checkButtonClick();
                if (clickedButton >= 0) {
                    action = handleButtonClick(clickedButton);
                }
            }
            break;

        case SDL_KEYDOWN:
            menuInput.handleKeyEvent(event);
            break;

        default:
            break;
    }

    return action;
}

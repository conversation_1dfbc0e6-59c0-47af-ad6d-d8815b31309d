#include "../../includes/Application/MenuSystem.hpp"

MenuSystem::MenuSystem()
    : main<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(textRenderer), settings<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(textRenderer), credits<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(textRenderer), instructions<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(textRenderer),
      mainMenu(mainMenuRenderer, mouseHandler, menuInputStub, HUMANGL_DEFAULT_WINDOW_WIDTH, HUMANGL_DEFAULT_WINDOW_HEIGHT),
      creditsMenu(creditsMenuRenderer, mouseHandler, menuInputStub, HUMANGL_DEFAULT_WINDOW_WIDTH, HUMANGL_DEFAULT_WINDOW_HEIGHT),
      instructionsMenu(instructionsMenu<PERSON>enderer, mouseHandler, menuInputStub, HUMANGL_DEFAULT_WINDOW_WIDTH, HUMANGL_DEFAULT_WINDOW_HEIGHT),
      currentState(MAIN_MENU), windowWidth(HUMANGL_DEFAULT_WINDOW_WIDTH), windowHeight(HUMANGL_DEFAULT_WINDOW_HEIGHT) {
    // Input handling is now integrated into SettingsMenuRenderer and EventHandler
}

MenuSystem::~MenuSystem() {
    cleanup();
}

bool MenuSystem::initialize(int winWidth, int winHeight) {
    windowWidth = winWidth;
    windowHeight = winHeight;
    
    // Initialize all subsystems
    if (!textRenderer.initialize()) {
        return false;
    }
    
    // MenuInput functionality moved to EventHandler - no initialization needed
    
    // Update renderers with window size
    mainMenuRenderer.updateWindowSize(windowWidth, windowHeight);
    settingsMenuRenderer.updateWindowSize(windowWidth, windowHeight);
    creditsMenuRenderer.updateWindowSize(windowWidth, windowHeight);
    instructionsMenuRenderer.updateWindowSize(windowWidth, windowHeight);
    
    // Menu initialization is handled by individual menu classes
    
    return true;
}

void MenuSystem::cleanup() {
    textRenderer.cleanup();
}

void MenuSystem::updateWindowSize(int width, int height) {
    windowWidth = width;
    windowHeight = height;
    mainMenuRenderer.updateWindowSize(width, height);
    settingsMenuRenderer.updateWindowSize(width, height);
    creditsMenuRenderer.updateWindowSize(width, height);
    instructionsMenuRenderer.updateWindowSize(width, height);

    // Update menu instances for new window size
    mainMenu.updateWindowSize(width, height);
    creditsMenu.updateWindowSize(width, height);
    instructionsMenu.updateWindowSize(width, height);
}

void MenuSystem::setState(AppState state) {
    // If we're entering settings from another state, reset to main settings page
    if (state == SETTINGS && currentState != SETTINGS) {
        settingsMenuRenderer.resetToMainPage();
    }

    currentState = state;
}

AppState MenuSystem::getState() const {
    return currentState;
}



MenuAction MenuSystem::handleEvent(const SDL_Event& event) {
    MenuAction action = MENU_ACTION_NONE;

    // Delegate event handling to the appropriate menu
    switch (currentState) {
        case MAIN_MENU:
            action = mainMenu.handleEvent(event);
            break;
        case SETTINGS:
            // Handle settings events directly through SettingsMenuRenderer
            action = handleSettingsEvent(event);
            // Handle settings customization input (now integrated into SettingsMenuRenderer)
            settingsMenuRenderer.handleInput();
            break;
        case CREDITS:
            action = creditsMenu.handleEvent(event);
            break;
        case INSTRUCTIONS:
            action = instructionsMenu.handleEvent(event);
            break;
        case SIMULATION:
            // Simulation events are handled by Application class
            break;
    }

    // Process menu actions using setState to ensure proper state transitions
    switch (action) {
        case MENU_ACTION_START_SIMULATION:
            setState(SIMULATION);
            break;
        case MENU_ACTION_SETTINGS:
            setState(SETTINGS);
            break;
        case MENU_ACTION_CREDITS:
            setState(CREDITS);
            break;
        case MENU_ACTION_INSTRUCTIONS:
            setState(INSTRUCTIONS);
            break;
        case MENU_ACTION_BACK_TO_MENU:
            setState(MAIN_MENU);
            break;
        default:
            break;
    }

    return action;
}

MenuAction MenuSystem::handleSettingsEvent(const SDL_Event& event) {
    MenuAction action = MENU_ACTION_NONE;

    if (event.type == SDL_MOUSEBUTTONDOWN) {
        if (event.button.button == SDL_BUTTON_LEFT) {
            float mouseX = static_cast<float>(event.button.x);
            float mouseY = static_cast<float>(event.button.y);

            // Check if click is on a button
            const std::vector<MenuButton>& buttons = settingsMenuRenderer.getButtons();
            for (size_t i = 0; i < buttons.size(); ++i) {
                if (isPointInButton(mouseX, mouseY, buttons[i])) {
                    action = settingsMenuRenderer.handleButtonClick(static_cast<int>(i));
                    break;
                }
            }

            // If no button was clicked, check for other UI interactions
            if (action == MENU_ACTION_NONE) {
                settingsMenuRenderer.handleMouseClick(mouseX, mouseY);
            }
        }
    } else if (event.type == SDL_MOUSEBUTTONUP) {
        if (event.button.button == SDL_BUTTON_LEFT) {
            settingsMenuRenderer.handleMouseUp();
        }
    } else if (event.type == SDL_MOUSEMOTION) {
        float mouseX = static_cast<float>(event.motion.x);
        float mouseY = static_cast<float>(event.motion.y);
        settingsMenuRenderer.updateHover(mouseX, mouseY);
    } else if (event.type == SDL_KEYDOWN) {
        if (event.key.keysym.sym == SDLK_ESCAPE) {
            action = MENU_ACTION_BACK_TO_MENU;
        }
    }

    return action;
}

bool MenuSystem::isPointInButton(float x, float y, const MenuButton& button) {
    return x >= button.x && x <= button.x + button.width &&
           y >= button.y && y <= button.y + button.height;
}

MenuAction MenuSystem::update() {
    MenuAction action = MENU_ACTION_NONE;

    // Delegate update to the appropriate menu
    switch (currentState) {
        case MAIN_MENU:
            action = mainMenu.update();
            break;
        case SETTINGS:
            // Settings updates are handled through SettingsMenuRenderer
            settingsMenuRenderer.updateButtonHover(mouseHandler);
            break;
        case CREDITS:
            action = creditsMenu.update();
            break;
        case INSTRUCTIONS:
            action = instructionsMenu.update();
            break;
        case SIMULATION:
            // Simulation input is now handled by EventHandler
            // No menu input handling needed here
            break;
    }

    // Process menu actions
    switch (action) {
        case MENU_ACTION_RETURN_TO_MENU:
            currentState = MAIN_MENU;
            break;
        case MENU_ACTION_EXIT:
            // Return to main menu when ESC is pressed
            currentState = MAIN_MENU;
            break;
        default:
            break;
    }

    return action;
}

void MenuSystem::render() {
    // Set menu background color from settings
    const Color& menuBgColor = settingsMenuRenderer.getMenuBackgroundColor();
    glClearColor(menuBgColor.r, menuBgColor.g, menuBgColor.b, menuBgColor.a);

    // Clear the screen with the background color
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

    switch (currentState) {
        case MAIN_MENU:
            mainMenu.render();
            break;
        case SETTINGS:
            settingsMenuRenderer.render();
            break;
        case CREDITS:
            creditsMenu.render();
            break;
        case INSTRUCTIONS:
            instructionsMenu.render();
            break;
        case SIMULATION:
            // Simulation rendering is handled by Application class
            break;
    }
}





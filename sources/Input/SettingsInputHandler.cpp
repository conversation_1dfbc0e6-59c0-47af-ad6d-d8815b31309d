#include "../../includes/Input/SettingsInputHandler.hpp"

SettingsInputHandler::SettingsInputHandler() 
    : keyboardState(SDL_GetKeyboardState(nullptr)), settings<PERSON>enderer(nullptr),
      plusKeyPressed(false), minusKey<PERSON>ressed(false), cKeyPressed(false) {
}

void SettingsInputHandler::setSettingsRenderer(SettingsMenuRenderer* renderer) {
    settingsRenderer = renderer;
}

void SettingsInputHandler::handleInput() {
    if (!settingsRenderer) return;
    
    // Only handle input when in body part detail page
    if (settingsRenderer->getCurrentPage() == BODY_PART_DETAIL) {
        handleCustomizationInput();
    }
}

void SettingsInputHandler::handleCustomizationInput() {
    // Handle scale adjustment with + and - keys
    bool currentPlusState = keyboardState[SDL_SCANCODE_KP_PLUS] || keyboardState[SDL_SCANCODE_EQUALS];
    bool currentMinusState = keyboardState[SDL_SCANCODE_KP_MINUS] || keyboardState[SDL_SCANCODE_MINUS];
    bool currentCState = keyboardState[SDL_SCANCODE_C];
    
    // Scale up with + key (only on key press, not hold)
    if (currentPlusState && !plusKeyPressed) {
        settingsRenderer->adjustBodyPartScale(1.1f);
        plusKeyPressed = true;
    } else if (!currentPlusState) {
        plusKeyPressed = false;
    }
    
    // Scale down with - key (only on key press, not hold)
    if (currentMinusState && !minusKeyPressed) {
        settingsRenderer->adjustBodyPartScale(0.9f);
        minusKeyPressed = true;
    } else if (!currentMinusState) {
        minusKeyPressed = false;
    }
    
    // Cycle color with C key (only on key press, not hold)
    if (currentCState && !cKeyPressed) {
        settingsRenderer->cycleBodyPartColor();
        cKeyPressed = true;
    } else if (!currentCState) {
        cKeyPressed = false;
    }
}

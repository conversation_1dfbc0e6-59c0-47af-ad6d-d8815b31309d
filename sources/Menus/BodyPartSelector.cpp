#include "../../includes/Menus/BodyPartSelector.hpp"

BodyPartSelector::BodyPartSelector(TextRenderer& textRenderer)
    : <PERSON><PERSON><PERSON><PERSON><PERSON>(textRenderer), text<PERSON><PERSON><PERSON>(textRenderer) {
    customizableBodyParts = getCustomizableBodyParts();
    initializeButtons();
}

void BodyPartSelector::initializeButtons() {
    buttons.clear();
    
    if (windowWidth <= 0 || windowHeight <= 0) {
        return;
    }
    
    // Create buttons for all customizable body parts (centered grid)
    std::vector<BodyPart> mainBodyParts = getCustomizableBodyParts();
    
    // Grid layout constants
    const int columns = HUMANGL_BODYPART_COLUMNS;
    float buttonWidth = HUMANGL_BODYPART_BUTTON_WIDTH;
    float buttonHeight = HUMANGL_BODYPART_BUTTON_HEIGHT;
    float spacing = HUMANGL_BODYPART_BUTTON_SPACING;
    float topMargin = HUMANGL_SETTINGS_CONTENT_START_Y;
    
    // Center the grid
    float horizontalSpacing = 30.0f; // More horizontal spacing between columns
    float totalGridWidth = columns * buttonWidth + (columns - 1) * horizontalSpacing;
    float centerX = (static_cast<float>(windowWidth) - totalGridWidth) / 2.0f;

    int row = 0, col = 0;
    for (size_t i = 0; i < mainBodyParts.size(); i++) {
        BodyPart part = mainBodyParts[i];
        float x = centerX + col * (buttonWidth + horizontalSpacing);
        float y = topMargin + row * spacing;
        
        std::string partName = getBodyPartName(part);
        buttons.emplace_back(x, y, buttonWidth, buttonHeight, partName);
        
        col++;
        if (col >= columns) {
            col = 0;
            row++;
        }
    }
    
    // Back buttons (horizontal layout, centered)
    float backButtonY = topMargin + (row + 1) * spacing + 40.0f;
    float backButtonSpacing = 30.0f; // More spacing to match grid
    float totalBackButtonWidth = 2 * buttonWidth + backButtonSpacing;
    float backButtonStartX = (static_cast<float>(windowWidth) - totalBackButtonWidth) / 2.0f;

    buttons.emplace_back(backButtonStartX, backButtonY, buttonWidth, buttonHeight, "Back to Settings");
    buttons.emplace_back(backButtonStartX + buttonWidth + backButtonSpacing, backButtonY, buttonWidth, buttonHeight, "Back to Main Menu");
}

void BodyPartSelector::render(const std::vector<MenuButton>& buttons) {
    renderTitle();
    renderInstructions();
    MenuRenderer::render(buttons);
}

void BodyPartSelector::render() {
    render(buttons);
}

const std::vector<MenuButton>& BodyPartSelector::getButtons() const {
    return buttons;
}

BodyPart BodyPartSelector::handleButtonClick(int buttonIndex) {
    std::vector<BodyPart> mainBodyParts = getCustomizableBodyParts();
    
    if (buttonIndex >= 0 && buttonIndex < static_cast<int>(mainBodyParts.size())) {
        return mainBodyParts[buttonIndex];
    }
    
    // Handle back buttons (they come after the body part buttons)
    // Return invalid body part to indicate navigation action
    return static_cast<BodyPart>(-1);
}

std::vector<BodyPart> BodyPartSelector::getCustomizableBodyParts() const {
    return {
        BODY_PART_HEAD,
        BODY_PART_NECK,
        BODY_PART_TORSO,
        BODY_PART_EYES,
        BODY_PART_LEFT_SHOULDER,
        BODY_PART_RIGHT_SHOULDER,
        BODY_PART_LEFT_UPPER_ARM,
        BODY_PART_RIGHT_UPPER_ARM,
        BODY_PART_LEFT_FOREARM,
        BODY_PART_RIGHT_FOREARM,
        BODY_PART_LEFT_THIGH,
        BODY_PART_RIGHT_THIGH,
        BODY_PART_LEFT_LOWER_LEG,
        BODY_PART_RIGHT_LOWER_LEG
    };
}

std::string BodyPartSelector::getBodyPartName(BodyPart part) const {
    switch (part) {
        case BODY_PART_HEAD: return "Head";
        case BODY_PART_NECK: return "Neck";
        case BODY_PART_TORSO: return "Torso";
        case BODY_PART_LEFT_SHOULDER: return "Left Shoulder";
        case BODY_PART_RIGHT_SHOULDER: return "Right Shoulder";
        case BODY_PART_LEFT_UPPER_ARM: return "Left Upper Arm";
        case BODY_PART_RIGHT_UPPER_ARM: return "Right Upper Arm";
        case BODY_PART_LEFT_FOREARM: return "Left Forearm";
        case BODY_PART_RIGHT_FOREARM: return "Right Forearm";
        case BODY_PART_LEFT_THIGH: return "Left Thigh";
        case BODY_PART_RIGHT_THIGH: return "Right Thigh";
        case BODY_PART_LEFT_LOWER_LEG: return "Left Lower Leg";
        case BODY_PART_RIGHT_LOWER_LEG: return "Right Lower Leg";
        case BODY_PART_EYES: return "Eyes";
        default: return "Unknown";
    }
}

void BodyPartSelector::renderTitle() {
    textRenderer.drawText(HUMANGL_SETTINGS_LEFT_MARGIN, HUMANGL_SETTINGS_TITLE_Y, "Body Customization", 1.0f, 1.0f, 1.0f);
}

void BodyPartSelector::renderInstructions() {
    float contentY = HUMANGL_SETTINGS_CONTENT_START_Y - 60.0f;
    float lineSpacing = HUMANGL_SETTINGS_LINE_SPACING;
    
    textRenderer.drawText(HUMANGL_SETTINGS_LEFT_MARGIN, contentY, "Select a body part to customize:", 0.9f, 0.9f, 0.9f);
    contentY += lineSpacing;
    textRenderer.drawText(HUMANGL_SETTINGS_LEFT_MARGIN + HUMANGL_SETTINGS_INDENT, contentY, "- Click any body part button below", 0.8f, 0.8f, 0.8f);
    contentY += lineSpacing;
    textRenderer.drawText(HUMANGL_SETTINGS_LEFT_MARGIN + HUMANGL_SETTINGS_INDENT, contentY, "- Customize colors and size for each part", 0.8f, 0.8f, 0.8f);
}

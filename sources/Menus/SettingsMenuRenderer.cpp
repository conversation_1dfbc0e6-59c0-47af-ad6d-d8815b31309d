#include "../../includes/Menus/SettingsMenuRenderer.hpp"
#include "../../includes/Input/MouseHandler.hpp"

SettingsMenuRenderer::SettingsMenu<PERSON>enderer(TextRenderer& textRenderer)
    : <PERSON><PERSON><PERSON><PERSON><PERSON>(textRenderer),
      main<PERSON><PERSON><PERSON>(textRenderer), bodyPart<PERSON>elector(textRenderer),
      bodyPart<PERSON><PERSON><PERSON>(textRenderer), backgroundCustomizer(textRenderer) {
    // Sync the shared logic components with the menu components
    syncLogicWithMenuComponents();
    initializeButtons();
}

void SettingsMenuRenderer::initializeButtons() {
    // Delegate button initialization to the appropriate component
    switch (logic.getCurrentPage()) {
        case SETTINGS_MAIN:
            mainMenu.initializeButtons();
            break;
        case BODY_CUSTOMIZATION:
            bodyPartSelector.initializeButtons();
            break;
        case BODY_PART_DETAIL:
            bodyPartEditor.initializeButtons();
            break;
        case BACKGROUND_CUSTOMIZATION:
            backgroundCustomizer.initializeButtons();
            break;
        case RESOLUTION_SETTINGS:
            mainMenu.initializeButtons(); // For now, use main menu for resolution settings
            break;
        default:
            mainMenu.initializeButtons();
            break;
    }
}

void SettingsMenuRenderer::render(const std::vector<MenuButton>& externalButtons) {
    // Delegate rendering to the appropriate component
    getCurrentMenuRenderer()->render(externalButtons);
}

void SettingsMenuRenderer::render() {
    // Delegate rendering to the appropriate component
    switch (logic.getCurrentPage()) {
        case SETTINGS_MAIN:
            mainMenu.render();
            break;
        case BODY_CUSTOMIZATION:
            bodyPartSelector.render();
            break;
        case BODY_PART_DETAIL:
            bodyPartEditor.render();
            break;
        case BACKGROUND_CUSTOMIZATION:
            backgroundCustomizer.render();
            break;
        case RESOLUTION_SETTINGS:
            mainMenu.render(); // For now, use main menu for resolution settings
            break;
        default:
            mainMenu.render();
            break;
    }
}

const std::vector<MenuButton>& SettingsMenuRenderer::getButtons() const {
    // Delegate to the appropriate component
    switch (logic.getCurrentPage()) {
        case SETTINGS_MAIN:
            return mainMenu.getButtons();
        case BODY_CUSTOMIZATION:
            return bodyPartSelector.getButtons();
        case BODY_PART_DETAIL:
            return bodyPartEditor.getButtons();
        case BACKGROUND_CUSTOMIZATION:
            return backgroundCustomizer.getButtons();
        case RESOLUTION_SETTINGS:
            return mainMenu.getButtons(); // For now, use main menu for resolution settings
        default:
            return mainMenu.getButtons();
    }
}

MenuAction SettingsMenuRenderer::handleButtonClick(int buttonIndex) {
    MenuAction action = logic.handleButtonClick(buttonIndex);

    // If page changed, reinitialize buttons for the new page
    if (action == MENU_ACTION_NONE) {
        initializeButtons();
    }

    return action;
}

// Page navigation (delegates to logic)
void SettingsMenuRenderer::setPage(SettingsPage page) {
    logic.setPage(page);
    initializeButtons();
}

void SettingsMenuRenderer::setSelectedBodyPart(BodyPart part) {
    logic.setSelectedBodyPart(part);
    bodyPartEditor.setSelectedBodyPart(part);
}

SettingsPage SettingsMenuRenderer::getCurrentPage() const {
    return logic.getCurrentPage();
}

void SettingsMenuRenderer::resetToMainPage() {
    logic.resetToMainPage();
    initializeButtons();
}

void SettingsMenuRenderer::updateWindowSize(int width, int height) {
    // Update base class window size
    MenuRenderer::updateWindowSize(width, height);

    // Update all component menu renderers
    mainMenu.updateWindowSize(width, height);
    bodyPartSelector.updateWindowSize(width, height);
    bodyPartEditor.updateWindowSize(width, height);
    backgroundCustomizer.updateWindowSize(width, height);

    // Reinitialize buttons for new window size
    initializeButtons();
}

// Body part customization (delegates to logic and syncs)
void SettingsMenuRenderer::cycleBodyPartColor() {
    // Apply change to both the main logic and the menu component logic
    logic.cycleBodyPartColor();
    bodyPartEditor.cycleBodyPartColor();
    syncBodyPartSettings();
}

void SettingsMenuRenderer::adjustBodyPartScale(float scaleMultiplier) {
    logic.adjustBodyPartScale(scaleMultiplier);
    bodyPartEditor.adjustBodyPartScale(scaleMultiplier);
    syncBodyPartSettings();
}

void SettingsMenuRenderer::setBodyPartScale(float newScale) {
    logic.setBodyPartScale(newScale);
    bodyPartEditor.setBodyPartScale(newScale);
    syncBodyPartSettings();
}

void SettingsMenuRenderer::resetBodyPartToDefault() {
    logic.resetBodyPartToDefault();
    bodyPartEditor.resetBodyPartToDefault();
    syncBodyPartSettings();
}

const BodyPartSettings& SettingsMenuRenderer::getBodyPartSettings(BodyPart part) const {
    // Always return from the main logic (single source of truth)
    return logic.getBodyPartSettings(part);
}

const std::map<BodyPart, BodyPartSettings>& SettingsMenuRenderer::getAllBodyPartSettings() const {
    // Always return from the main logic (single source of truth)
    return logic.getAllBodyPartSettings();
}

// Background color customization (delegates to logic and syncs)
void SettingsMenuRenderer::cycleMenuBackgroundColor() {
    // Apply change to both the main logic and the menu component logic
    logic.cycleMenuBackgroundColor();
    backgroundCustomizer.cycleMenuBackgroundColor();
}

void SettingsMenuRenderer::cycleSimulationBackgroundColor() {
    logic.cycleSimulationBackgroundColor();
    backgroundCustomizer.cycleSimulationBackgroundColor();
}

void SettingsMenuRenderer::resetColorsToDefault() {
    logic.resetColorsToDefault();
    backgroundCustomizer.resetColorsToDefault();
}

const Color& SettingsMenuRenderer::getMenuBackgroundColor() const {
    // Always return from the main logic (single source of truth)
    return logic.getMenuBackgroundColor();
}

const Color& SettingsMenuRenderer::getSimulationBackgroundColor() const {
    // Always return from the main logic (single source of truth)
    return logic.getSimulationBackgroundColor();
}

// Mouse interaction (delegates to logic through rendering components and syncs)
bool SettingsMenuRenderer::handleMouseClick(float mouseX, float mouseY) {
    // Delegate to the rendering components which handle UI positioning
    bool handled = false;
    switch (logic.getCurrentPage()) {
        case BODY_PART_DETAIL:
            handled = bodyPartEditor.handleMouseClick(mouseX, mouseY);
            if (handled) {
                // Sync any changes back to main logic
                logic.getBodyPartEditorLogic().setBodyPartSettings(bodyPartEditor.getAllBodyPartSettings());
            }
            break;
        case BACKGROUND_CUSTOMIZATION:
            handled = backgroundCustomizer.handleMouseClick(mouseX, mouseY);
            // Background color changes are handled through the synchronized methods
            break;
        default:
            break;
    }
    return handled;
}

void SettingsMenuRenderer::updateHover(float mouseX, float mouseY) {
    switch (logic.getCurrentPage()) {
        case BODY_PART_DETAIL:
            bodyPartEditor.updateHover(mouseX, mouseY);
            break;
        default:
            break;
    }
}

void SettingsMenuRenderer::handleMouseUp() {
    switch (logic.getCurrentPage()) {
        case BODY_PART_DETAIL:
            bodyPartEditor.handleMouseUp();
            break;
        default:
            break;
    }
}

// Input handling (delegates to logic and syncs)
void SettingsMenuRenderer::handleInput() {
    // Handle input in both the main logic and the menu components
    logic.handleInput();

    // Also handle input in the current menu component
    switch (logic.getCurrentPage()) {
        case BODY_PART_DETAIL:
            bodyPartEditor.handleInput();
            // Sync any changes back to main logic
            logic.getBodyPartEditorLogic().setBodyPartSettings(bodyPartEditor.getAllBodyPartSettings());
            break;
        case BACKGROUND_CUSTOMIZATION:
            backgroundCustomizer.handleInput();
            // Background colors are handled through the synchronized methods above
            break;
        default:
            break;
    }
}

// Helper method to get the current menu renderer
MenuRenderer* SettingsMenuRenderer::getCurrentMenuRenderer() {
    switch (logic.getCurrentPage()) {
        case SETTINGS_MAIN:
            return &mainMenu;
        case BODY_CUSTOMIZATION:
            return &bodyPartSelector;
        case BODY_PART_DETAIL:
            return &bodyPartEditor;
        case BACKGROUND_CUSTOMIZATION:
            return &backgroundCustomizer;
        case RESOLUTION_SETTINGS:
            return &mainMenu; // For now, use main menu for resolution settings
        default:
            return &mainMenu;
    }
}

const MenuRenderer* SettingsMenuRenderer::getCurrentMenuRenderer() const {
    switch (logic.getCurrentPage()) {
        case SETTINGS_MAIN:
            return &mainMenu;
        case BODY_CUSTOMIZATION:
            return &bodyPartSelector;
        case BODY_PART_DETAIL:
            return &bodyPartEditor;
        case BACKGROUND_CUSTOMIZATION:
            return &backgroundCustomizer;
        case RESOLUTION_SETTINGS:
            return &mainMenu; // For now, use main menu for resolution settings
        default:
            return &mainMenu;
    }
}

void SettingsMenuRenderer::syncBodyPartSettings() {
    // Sync body part settings from main logic to rendering component
    bodyPartEditor.setBodyPartSettings(logic.getAllBodyPartSettings());
}

void SettingsMenuRenderer::syncBackgroundColors() {
    // Background colors are now managed by the logic component
    // No sync needed as rendering components get colors from logic
}

void SettingsMenuRenderer::syncLogicWithMenuComponents() {
    // Sync the main logic component's state with the menu components' logic
    // This ensures that all components work with the same data

    // Copy settings from main logic to menu component logic
    bodyPartEditor.getLogic().setBodyPartSettings(logic.getAllBodyPartSettings());

    // Copy the current selected body part
    bodyPartEditor.getLogic().setSelectedBodyPart(logic.getSelectedBodyPart());

    // Sync background colors - copy from main logic to menu component
    // We need to ensure both logic components have the same color state
    // For now, both start with default values, but we should sync any changes

    // Sync initial settings to rendering components
    syncBodyPartSettings();
}

void SettingsMenuRenderer::updateButtonHover(MouseHandler& mouseHandler) {
    // Delegate button hover updates to the appropriate component
    switch (logic.getCurrentPage()) {
        case SETTINGS_MAIN:
            mainMenu.updateButtonHover(mouseHandler);
            break;
        case BODY_CUSTOMIZATION:
            bodyPartSelector.updateButtonHover(mouseHandler);
            break;
        case BODY_PART_DETAIL:
            bodyPartEditor.updateButtonHover(mouseHandler);
            break;
        case BACKGROUND_CUSTOMIZATION:
            backgroundCustomizer.updateButtonHover(mouseHandler);
            break;
        case RESOLUTION_SETTINGS:
            mainMenu.updateButtonHover(mouseHandler);
            break;
    }
}
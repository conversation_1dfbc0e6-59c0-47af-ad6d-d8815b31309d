#include "../../includes/Menus/CreditsMenuRenderer.hpp"

CreditsMenuRenderer::CreditsMenuRenderer(TextRenderer& textRenderer)
    : <PERSON>u<PERSON><PERSON><PERSON>(textRenderer) {
    initializeButtons();
}

void CreditsMenuRenderer::initializeButtons() {
    float buttonWidth = 200.0f;
    float buttonHeight = 50.0f;
    float centerX = (static_cast<float>(windowWidth) - buttonWidth) / 2.0f;
    float startY = static_cast<float>(windowHeight) / 2.0f + 150.0f;

    buttons.clear();
    buttons.push_back(MenuButton(centerX, startY, buttonWidth, buttonHeight, "Back to Menu"));
}

void CreditsMenuRenderer::render(const std::vector<MenuButton>& externalButtons) {
    setup2D();
    clearScreen();

    renderTitle();
    renderCreditsContent();

    // Draw menu buttons
    for (const auto& button : externalButtons) {
        drawButton(button);
    }
}

void CreditsMenuRenderer::render() {
    setup2D();
    clearScreen();

    renderTitle();
    renderCreditsContent();

    // Draw our own buttons
    for (const auto& button : buttons) {
        drawButton(button);
    }
}

const std::vector<MenuButton>& CreditsMenuRenderer::getButtons() const {
    return buttons;
}

MenuAction CreditsMenuRenderer::handleButtonClick(int buttonIndex) {
    switch (buttonIndex) {
        case 0: // Back to Menu
            return MENU_ACTION_BACK_TO_MENU;
        default:
            return MENU_ACTION_NONE;
    }
}

void CreditsMenuRenderer::renderTitle() {
    // Draw credits title
    float titleX = static_cast<float>(windowWidth) / 2.0f - 50.0f;
    float titleY = 100.0f;
    textRenderer.drawText(titleX, titleY, "Credits", 1.0f, 1.0f, 1.0f);
}

void CreditsMenuRenderer::renderCreditsContent() {
    // Calculate column positions
    float leftColumnX = 50.0f;
    float rightColumnX = static_cast<float>(windowWidth) / 2.0f + 50.0f;
    float contentY = 200.0f;
    float lineSpacing = 30.0f;

    // Left Column - Technologies Used
    float leftY = contentY;
    textRenderer.drawText(leftColumnX, leftY, "Developed with:", 0.9f, 0.9f, 0.9f);
    leftY += lineSpacing;
    textRenderer.drawText(leftColumnX + 20.0f, leftY, "- OpenGL for 3D graphics", 0.8f, 0.8f, 0.8f);
    leftY += lineSpacing;
    textRenderer.drawText(leftColumnX + 20.0f, leftY, "- SDL2 for window management", 0.8f, 0.8f, 0.8f);
    leftY += lineSpacing;
    textRenderer.drawText(leftColumnX + 20.0f, leftY, "- C++14 for core implementation", 0.8f, 0.8f, 0.8f);
    leftY += lineSpacing * 1.5f;

    textRenderer.drawText(leftColumnX, leftY, "Architecture:", 0.9f, 0.9f, 0.9f);
    leftY += lineSpacing;
    textRenderer.drawText(leftColumnX + 20.0f, leftY, "- Modular design pattern", 0.8f, 0.8f, 0.8f);
    leftY += lineSpacing;
    textRenderer.drawText(leftColumnX + 20.0f, leftY, "- Object-oriented structure", 0.8f, 0.8f, 0.8f);
    leftY += lineSpacing;
    textRenderer.drawText(leftColumnX + 20.0f, leftY, "- Event-driven system", 0.8f, 0.8f, 0.8f);

    // Right Column - Features
    float rightY = contentY;
    textRenderer.drawText(rightColumnX, rightY, "CoreFeatures:", 0.9f, 0.9f, 0.9f);
    rightY += lineSpacing;
    textRenderer.drawText(rightColumnX + 20.0f, rightY, "- Hierarchical skeletal animation", 0.8f, 0.8f, 0.8f);
    rightY += lineSpacing;
    textRenderer.drawText(rightColumnX + 20.0f, rightY, "- Interactive joint control", 0.8f, 0.8f, 0.8f);
    rightY += lineSpacing;
    textRenderer.drawText(rightColumnX + 20.0f, rightY, "- Multiple animation modes", 0.8f, 0.8f, 0.8f);
    rightY += lineSpacing;
    textRenderer.drawText(rightColumnX + 20.0f, rightY, "- Real-time rendering", 0.8f, 0.8f, 0.8f);
    rightY += lineSpacing * 1.5f;

    textRenderer.drawText(rightColumnX, rightY, "Controls:", 0.9f, 0.9f, 0.9f);
    rightY += lineSpacing;
    textRenderer.drawText(rightColumnX + 20.0f, rightY, "- SPACE: Walking animation", 0.8f, 0.8f, 0.8f);
    rightY += lineSpacing;
    textRenderer.drawText(rightColumnX + 20.0f, rightY, "- P: Jump animation", 0.8f, 0.8f, 0.8f);
    rightY += lineSpacing;
    textRenderer.drawText(rightColumnX + 20.0f, rightY, "- J: Dancing animation", 0.8f, 0.8f, 0.8f);
    rightY += lineSpacing;
    textRenderer.drawText(rightColumnX + 20.0f, rightY, "- K: Kung Fu animation", 0.8f, 0.8f, 0.8f);
    rightY += lineSpacing;
    textRenderer.drawText(rightColumnX + 20.0f, rightY, "- A/D: Rotate character", 0.8f, 0.8f, 0.8f);
}
